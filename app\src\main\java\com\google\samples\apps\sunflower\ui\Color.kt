/*
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.samples.apps.sunflower.ui

import androidx.compose.ui.graphics.Color

val md_theme_light_primary = Color(0xFF246D00)
val md_theme_light_onPrimary = Color(0xFFFFFFFF)
val md_theme_light_primaryContainer = Color(0xFFA6F780)
val md_theme_light_onPrimaryContainer = Color(0xFF062100)
val md_theme_light_secondary = Color(0xFF55624C)
val md_theme_light_onSecondary = Color(0xFFFFFFFF)
val md_theme_light_secondaryContainer = Color(0xFFD8E7CB)
val md_theme_light_onSecondaryContainer = Color(0xFF131F0D)
val md_theme_light_tertiary = Color(0xFF386667)
val md_theme_light_onTertiary = Color(0xFFFFFFFF)
val md_theme_light_tertiaryContainer = Color(0xFFBBEBEC)
val md_theme_light_onTertiaryContainer = Color(0xFF002021)
val md_theme_light_error = Color(0xFFBA1A1A)
val md_theme_light_errorContainer = Color(0xFFFFDAD6)
val md_theme_light_onError = Color(0xFFFFFFFF)
val md_theme_light_onErrorContainer = Color(0xFF410002)
val md_theme_light_background = Color(0xFFFDFDF6)
val md_theme_light_onBackground = Color(0xFF1A1C18)
val md_theme_light_surface = Color(0xFFFDFDF6)
val md_theme_light_onSurface = Color(0xFF1A1C18)
val md_theme_light_surfaceVariant = Color(0xFFDFE4D7)
val md_theme_light_onSurfaceVariant = Color(0xFF43483E)
val md_theme_light_outline = Color(0xFF73796D)
val md_theme_light_inverseOnSurface = Color(0xFFF1F1EA)
val md_theme_light_inverseSurface = Color(0xFF2F312D)
val md_theme_light_inversePrimary = Color(0xFF8BDA67)
val md_theme_light_shadow = Color(0xFF000000)
val md_theme_light_surfaceTint = Color(0xFF246D00)
val md_theme_light_outlineVariant = Color(0xFFC3C8BB)
val md_theme_light_scrim = Color(0xFF000000)

val md_theme_dark_primary = Color(0xFF8BDA67)
val md_theme_dark_onPrimary = Color(0xFF0F3900)
val md_theme_dark_primaryContainer = Color(0xFF195200)
val md_theme_dark_onPrimaryContainer = Color(0xFFA6F780)
val md_theme_dark_secondary = Color(0xFFBCCBB0)
val md_theme_dark_onSecondary = Color(0xFF273421)
val md_theme_dark_secondaryContainer = Color(0xFF3E4A36)
val md_theme_dark_onSecondaryContainer = Color(0xFFD8E7CB)
val md_theme_dark_tertiary = Color(0xFFA0CFD0)
val md_theme_dark_onTertiary = Color(0xFF003738)
val md_theme_dark_tertiaryContainer = Color(0xFF1E4E4F)
val md_theme_dark_onTertiaryContainer = Color(0xFFBBEBEC)
val md_theme_dark_error = Color(0xFFFFB4AB)
val md_theme_dark_errorContainer = Color(0xFF93000A)
val md_theme_dark_onError = Color(0xFF690005)
val md_theme_dark_onErrorContainer = Color(0xFFFFDAD6)
val md_theme_dark_background = Color(0xFF1A1C18)
val md_theme_dark_onBackground = Color(0xFFE3E3DC)
val md_theme_dark_surface = Color(0xFF1A1C18)
val md_theme_dark_onSurface = Color(0xFFE3E3DC)
val md_theme_dark_surfaceVariant = Color(0xFF43483E)
val md_theme_dark_onSurfaceVariant = Color(0xFFC3C8BB)
val md_theme_dark_outline = Color(0xFF8D9287)
val md_theme_dark_inverseOnSurface = Color(0xFF1A1C18)
val md_theme_dark_inverseSurface = Color(0xFFE3E3DC)
val md_theme_dark_inversePrimary = Color(0xFF246D00)
val md_theme_dark_shadow = Color(0xFF000000)
val md_theme_dark_surfaceTint = Color(0xFF8BDA67)
val md_theme_dark_outlineVariant = Color(0xFF43483E)
val md_theme_dark_scrim = Color(0xFF000000)

val seed = Color(0xFF256F00)
