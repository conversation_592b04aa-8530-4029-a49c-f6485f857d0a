# Copyright 2018 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# GOOGLE SAMPLE PACKAGING DATA
#
# This file is used by Google as part of our samples packaging process.
# End users may safely ignore this file. It has no relevance to other systems.
---
status:       PUBLISHED
technologies: [Android, JetpackCompose, Coroutines]
categories:
  - Getting Started
  - Jetpack
  - AndroidTesting
  - AndroidArchitecture
  - AndroidArchitectureUILayer
  - AndroidArchitectureDataLayer
  - AndroidArchitectureStateProduction
  - AndroidArchitectureStateHolder
  - AndroidArchitectureUIEvents
  - JetpackComposeArchitectureAndState
  - JetpackComposeMigrationAndInterop
  - JetpackComposeDesignSystems
  - JetpackComposeNavigation
  - JetpackComposeAnimation
  - JetpackComposeTesting
languages:    [Kotlin]
solutions:
  - Mobile
  - Flow
  - JetpackHilt
  - JetpackRoom
  - JetpackWorkManager
  - JetpackNavigation
  - JetpackLifecycle
github:       android/sunflower
level:        INTERMEDIATE
icon:         screenshots/ic_launcher-web.png
apiRefs:
    - android:android.support.constraint.ConstraintLayout
license: apache2
