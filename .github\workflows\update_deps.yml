#
# Copyright 2024 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

name: Update Versions / Dependencies

on:
  schedule:
    - cron: '9 0 1 * *'
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Copy CI gradle.properties
      run: mkdir -p ~/.gradle ; cp .github/ci-gradle.properties ~/.gradle/gradle.properties
    - name: set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: 17
        distribution: 'zulu'
        cache: gradle

    - name: Update dependencies
      run: ./gradlew versionCatalogUpdate
    - name: Create pull request
      id: cpr
      uses: peter-evans/create-pull-request@v4
      with:
        token: ${{ secrets.PAT }}
        commit-message: 🤖 Update Dependencies
        committer: compose-devrel-github-bot <<EMAIL>>
        author: compose-devrel-github-bot <<EMAIL>>
        signoff: false
        branch: bot-update-deps
        delete-branch: true
        title: '🤖 Update Dependencies'
        body: Updated depedencies
        reviewers: ${{ github.actor }}
