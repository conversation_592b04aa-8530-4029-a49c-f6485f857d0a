# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: Android CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:

  build:
    name: Build
    runs-on: ubuntu-20.04

    steps:
      - uses: actions/checkout@v3
      - name: set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'
      - name: Build project
        run: .github/scripts/gradlew_recursive.sh assembleDebug
      - name: Zip artifacts
        run: zip -r assemble.zip . -i '**/build/*.apk' '**/build/*.aab' '**/build/*.aar' '**/build/*.so'
      - name: Upload artifacts
        uses: actions/upload-artifact@v1
        with:
          name: assemble
          path: assemble.zip
#
#  Disabling androidTest temporarily due to the stability issue b/251319989
#  androidTest:
#    needs: build
#    runs-on: macOS-latest
#    timeout-minutes: 45
#
#    steps:
#      - uses: actions/setup-java@v3
#        with:
#          distribution: 'temurin'
#          java-version: '11'
#      - uses: actions/checkout@v3
#
#      - name: Setup Android SDK
#        uses: android-actions/setup-android@v2
#
#      - name: Checkout
#        uses: actions/checkout@v3
#
#      - name: Run instrumented tests with GMD
#        run: ./gradlew pixel2api27DebugAndroidTest -Pandroid.testoptions.manageddevices.emulator.gpu="swiftshader_indirect" -Pandroid.experimental.testOptions.managedDevices.setupTimeoutMinutes=20 -Pandroid.experimental.testOptions.managedDevices.emulator.showKernelLogging=true --info
#
#      - name: Upload test reports
#        if: always()
#        uses: actions/upload-artifact@v3
#        with:
#          name: test-reports
#          path: |
#            '*/build/outputs/androidTest-results/'
#            '!**/*"*' # Couldn't exclude a file with double quotation. Revisit the path once b/242988834 is fixed
